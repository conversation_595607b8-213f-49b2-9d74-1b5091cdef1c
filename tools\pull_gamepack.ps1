# Handle Ctrl+C gracefully
$global:shouldStop = $false

# Register event handler for Ctrl+C
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    $global:shouldStop = $true
    Write-Host "`nReceived interrupt signal. Stopping..." -ForegroundColor Yellow
}

# Also handle console cancel key press
[Console]::TreatControlCAsInput = $false
$null = [Console]::CancelKeyPress.Add({
    param($sender, $e)
    $e.Cancel = $true
    $global:shouldStop = $true
    Write-Host "`nReceived Ctrl+C. Stopping gracefully..." -ForegroundColor Yellow
})

# Check if adb is available in PATH
try {
    $null = Get-Command adb -ErrorAction Stop
} catch {
    Write-Error "ADB is not found in PATH. Please ensure Android SDK Platform Tools is installed and added to PATH."
    exit 1
}

function Write-Log {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "$timestamp - $Message"
}

function Find-JagexAppFolder {
    Write-Log "Searching for Jagex app folder in /data/app/..."

    # Check for interrupt
    if ($global:shouldStop) {
        Write-Host "Operation cancelled by user." -ForegroundColor Yellow
        return $null
    }

    # Try direct find command first (more efficient)
    Write-Log "Trying direct search using find command..."
    $findResult = & adb shell "su -c 'find /data/app/ -name \"*com.jagex.oldscape.android*\" -type d 2>/dev/null | head -1'" 2>$null
    Write-Log "Direct find result: '$findResult' (exit code: $LASTEXITCODE)"

    if ($LASTEXITCODE -eq 0 -and $findResult) {
        $findResult = $findResult.Trim()
        if ($findResult -ne "") {
            Write-Log "Found Jagex app folder using find: $findResult"
            return $findResult
        }
    }

    # Fallback to directory-by-directory search
    Write-Log "Direct search failed, trying directory-by-directory search..."

    # Get list of directories in /data/app/ using su
    $appDirs = & adb shell "su -c 'ls /data/app/'" 2>$null
    Write-Log "Found $($appDirs.Count) directories in /data/app/ (exit code: $LASTEXITCODE)"

    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to list /data/app/ directory. Make sure device is rooted and su access is available."
        return $null
    }

    # Find directory containing com.jagex.oldscape.android
    foreach ($dir in $appDirs) {
        # Check for interrupt in loop
        if ($global:shouldStop) {
            Write-Host "Operation cancelled by user." -ForegroundColor Yellow
            return $null
        }

        $dir = $dir.Trim()
        if ($dir -eq "") { continue }

        $fullPath = "/data/app/$dir"
        Write-Log "Checking directory: $fullPath"

        # First, let's see what's actually in this directory
        $dirContents = & adb shell "su -c 'cd \"$fullPath\" && ls -la'" 2>$null
        Write-Log "Directory contents of $fullPath (exit code: $LASTEXITCODE):"
        if ($dirContents) {
            foreach ($line in $dirContents) {
                Write-Log "  $line"
            }
        } else {
            Write-Log "  [No contents or permission denied]"
        }

        # Use find within this specific directory to avoid permission issues
        $jagexSubDir = & adb shell "su -c 'cd \"$fullPath\" && find . -maxdepth 1 -name \"*com.jagex.oldscape.android*\" -type d'" 2>$null
        Write-Log "Find result in $fullPath : '$jagexSubDir' (exit code: $LASTEXITCODE)"

        if ($LASTEXITCODE -eq 0 -and $jagexSubDir) {
            $jagexSubDir = $jagexSubDir.Trim()
            if ($jagexSubDir -ne "") {
                Write-Log "Found Jagex app folder: $jagexSubDir"
                return $jagexSubDir
            }
        }

        # Also try a broader search pattern
        $broadSearch = & adb shell "su -c 'cd \"$fullPath\" && find . -maxdepth 2 -name \"*jagex*\" -o -name \"*oldscape*\"'" 2>$null
        if ($broadSearch) {
            Write-Log "Broad search results in $fullPath :"
            foreach ($result in $broadSearch) {
                Write-Log "  Found: $result"
            }
        }
    }

    Write-Error "Could not find com.jagex.oldscape.android folder in /data/app/"
    return $null
}

function Pull-GamepackFiles {
    param($JagexPath)

    Write-Log "Starting gamepack file extraction from: $JagexPath"

    # Check for interrupt
    if ($global:shouldStop) {
        Write-Host "Operation cancelled by user." -ForegroundColor Yellow
        return $false
    }

    # Create output directory if it doesn't exist
    $outputDir = "./gamepack"
    if (!(Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        Write-Log "Created output directory: $outputDir"
    }

    # Files to pull
    $filesToPull = @(
        @{ Source = "$JagexPath/base.apk"; Dest = "$outputDir/base.apk" },
        @{ Source = "$JagexPath/split_config.x86_64.apk"; Dest = "$outputDir/split_config.x86_64.apk" },
        @{ Source = "$JagexPath/lib"; Dest = "$outputDir/lib" }
    )

    $successCount = 0
    $totalFiles = $filesToPull.Count

    foreach ($file in $filesToPull) {
        # Check for interrupt before each file
        if ($global:shouldStop) {
            Write-Host "Operation cancelled by user during file transfer." -ForegroundColor Yellow
            return $false
        }

        Write-Log "Pulling: $($file.Source) -> $($file.Dest)"

        # Check if source exists first using su
        $checkResult = & adb shell "su -c 'test -e \"$($file.Source)\" && echo exists || echo missing'" 2>$null

        if ($checkResult -like "*missing*" -or $LASTEXITCODE -ne 0) {
            Write-Warning "Source file/directory not found: $($file.Source)"
            continue
        }

        # Check for interrupt before pulling
        if ($global:shouldStop) {
            Write-Host "Operation cancelled by user during file transfer." -ForegroundColor Yellow
            return $false
        }

        # For files in /data/app/, we need to copy them to an accessible location first
        $tempPath = "/sdcard/temp_gamepack_$(Get-Random)"
        $fileName = Split-Path $file.Source -Leaf
        $tempFilePath = "$tempPath/$fileName"

        Write-Log "Creating temporary directory and copying file with su permissions..."

        # Create temp directory and copy file using su
        $copyResult = & adb shell "su -c 'mkdir -p \"$tempPath\" && cp -r \"$($file.Source)\" \"$tempFilePath\" && chmod -R 755 \"$tempPath\"'" 2>&1

        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to copy $($file.Source) to temporary location"
            Write-Error "Copy output: $copyResult"
            continue
        }

        # Now pull from the accessible temporary location
        $pullResult = & adb pull "$tempFilePath" "$($file.Dest)" 2>&1

        # Clean up temporary file
        & adb shell "su -c 'rm -rf \"$tempPath\"'" 2>$null

        if ($LASTEXITCODE -eq 0) {
            Write-Log "Successfully pulled: $($file.Source)"
            $successCount++
        } else {
            Write-Error "Failed to pull: $($file.Source)"
            Write-Error "ADB output: $pullResult"
        }
    }

    Write-Log "Gamepack extraction completed. Successfully pulled $successCount out of $totalFiles files."

    if ($successCount -eq $totalFiles) {
        Write-Host "All gamepack files successfully extracted to: $outputDir" -ForegroundColor Green
        return $true
    } else {
        Write-Warning "Some files could not be extracted. Check the logs above for details."
        return $false
    }
}

# Main execution
Write-Log "Starting gamepack extraction process..."
Write-Host "Note: This script requires a rooted Android device with su access." -ForegroundColor Cyan
Write-Host "Press Ctrl+C to cancel at any time." -ForegroundColor Cyan

try {
    # Find the Jagex app folder
    $jagexPath = Find-JagexAppFolder

    if ($global:shouldStop) {
        Write-Host "Operation cancelled by user." -ForegroundColor Yellow
        exit 130  # Standard exit code for Ctrl+C
    }

    if ($jagexPath -eq $null) {
        Write-Error "Cannot proceed without finding the Jagex app folder."
        exit 1
    }

    # Pull the gamepack files
    $success = Pull-GamepackFiles -JagexPath $jagexPath

    if ($global:shouldStop) {
        Write-Host "Operation cancelled by user." -ForegroundColor Yellow
        exit 130  # Standard exit code for Ctrl+C
    }

    if ($success) {
        Write-Log "Gamepack extraction completed successfully!"
        exit 0
    } else {
        Write-Error "Gamepack extraction completed with errors."
        exit 1
    }
}
catch {
    if ($global:shouldStop) {
        Write-Host "Operation cancelled by user." -ForegroundColor Yellow
        exit 130
    } else {
        Write-Error "An unexpected error occurred: $_"
        exit 1
    }
}
finally {
    # Cleanup: Unregister event handlers
    try {
        Get-EventSubscriber | Where-Object { $_.SourceIdentifier -eq "PowerShell.Exiting" } | Unregister-Event
    } catch {
        # Ignore cleanup errors
    }
}